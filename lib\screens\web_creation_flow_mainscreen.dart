import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import '../utils/font_manager.dart';
import '../theme/app_colors.dart';

class WebCreationFlowMainScreen extends StatefulWidget {
  const WebCreationFlowMainScreen({Key? key}) : super(key: key);

  @override
  State<WebCreationFlowMainScreen> createState() =>
      _WebCreationFlowMainScreenState();
}

class _WebCreationFlowMainScreenState extends State<WebCreationFlowMainScreen> {
  String _selectedTab = 'Recent';
  int _currentPage = 0;
  final int _itemsPerPage = 10;
  String _sortColumn = 'lastOpened';
  bool _sortAscending = false;

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  // Category filter functionality
  String? _selectedCategoryFilter;

  // Hover state tracking for table rows
  int? _hoveredRowIndex;

  // Hover state tracking for sortable headers
  String? _hoveredHeaderColumn;

  // Static data for the table
  final List<Map<String, dynamic>> _allData = [
    // Role entries
    {
      'fileName': 'CEO',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': true,
    },
    {
      'fileName': 'Product Manager',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': false,
    },
    {
      'fileName': 'Admin',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 21),
      'isFavorite': false,
    },
    // Object entries
    {
      'fileName': 'Employee',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
    },
    {
      'fileName': 'Order',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
    },
    {
      'fileName': 'Customer',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 21),
      'isFavorite': true,
    },
    {
      'fileName': 'Product',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 20),
      'isFavorite': true,
    },
    // Solution entries
    {
      'fileName': 'Customer order product',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
    },
    {
      'fileName': 'Inventory System',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': true,
    },
    {
      'fileName': 'Sales Dashboard',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
    },
    {
      'fileName': 'Payment System',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 20),
      'isFavorite': false,
    },
    // Project entries
    {
      'fileName': 'E-commerce Platform',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 25),
      'isFavorite': true,
    },
    {
      'fileName': 'CRM System',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
    },
    {
      'fileName': 'Mobile App Development',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': true,
    },
    {
      'fileName': 'Data Analytics Dashboard',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
    },
  ];

  List<Map<String, dynamic>> get _filteredData {
    List<Map<String, dynamic>> filtered = [];

    switch (_selectedTab) {
      case 'Favourite':
        filtered =
            _allData.where((item) => item['isFavorite'] == true).toList();
        break;
      default: // Recent
        filtered = List.from(_allData);
    }

    // Apply category filter
    if (_selectedCategoryFilter != null) {
      filtered = filtered
          .where((item) => item['type'] == _selectedCategoryFilter)
          .toList();
    }

    // Apply search filter
    final searchText = _searchController.text.toLowerCase();
    if (searchText.isNotEmpty) {
      filtered = filtered
          .where((item) =>
              (item['fileName']?.toLowerCase().contains(searchText) ?? false) ||
              (item['type']?.toLowerCase().contains(searchText) ?? false))
          .toList();
    }

    // Sort the data by last opened (most recent first)
    filtered.sort((a, b) {
      DateTime aDate = a['lastOpened'];
      DateTime bDate = b['lastOpened'];
      return bDate.compareTo(aDate);
    });

    return filtered;
  }

  List<Map<String, dynamic>> get _paginatedData {
    final startIndex = _currentPage * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, _filteredData.length);
    return _filteredData.sublist(startIndex, endIndex);
  }

  int get _totalPages => (_filteredData.length / _itemsPerPage).ceil();

  void _sortTable(String column) {
    setState(() {
      if (_sortColumn == column) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = column;
        _sortAscending = true;
      }
      _currentPage = 0; // Reset to first page when sorting
    });
  }

  void _showDiscoverModal() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _buildModal(
          title: 'How Discover Works - Your AI-Guided Journey',
          content: _buildDiscoverModalContent(),
        );
      },
    );
  }

  void _showDevelopModal() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _buildModal(
          title: 'How Development Works - Your Custom Build Journey',
          content: _buildDevelopModalContent(),
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_filterData);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterData);
    _searchController.dispose();
    super.dispose();
  }

  // Filter data based on search text
  void _filterData() {
    setState(() {
      _currentPage = 0; // Reset to first page when filtering
    });
  }

  // Toggle search bar visibility
  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar
        _searchController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
        child: Row(
          children: [
            Expanded(
              child: SizedBox(),
            ),
            Expanded(
              flex: 10,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Fixed header
                  CustomTabHeader(),
                  const SizedBox(height: 20),

                  // Scrollable content
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(AppSpacing.sm),
                        child: Column(
                          children: [
                            _buildMainContent(),
                            const SizedBox(height: 20),
                            // Bottom section with constrained height
                            SizedBox(
                              height: MediaQuery.of(context).size.height * 0.85,
                              child: _buildBottomSection(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SizedBox(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(), // Left placeholder

        // Center-aligned tabs
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCenterTabItem(
                icon: Icons.library_books, label: 'My Library', isActive: true),
            const SizedBox(width: 16),
            _buildCenterTabItem(icon: Icons.event_note, label: 'Organizer'),
            const SizedBox(width: 16),
            _buildCenterTabItem(icon: Icons.bug_report, label: 'Testing'),
          ],
        ),

        // Right-aligned "More templates"
        Text(
          'More templates',
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textBlue, // Text color
            decoration: TextDecoration.underline, // Underline enabled
            decorationColor: AppColors.textBlue, // Underline color set to blue
          ),
        ),
      ],
    );
  }

  Widget _buildCenterTabItem(
      {required IconData icon, required String label, bool isActive = false}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isActive ? AppColors.primaryBlue : AppColors.textGreyColor,
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isActive ? AppColors.primaryBlue : AppColors.textGreyColor,
          ),
        ),
      ],
    );
  }

  Widget _buildMainContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left side - Discover and Develop sections together
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width / 4.7,
              child: _buildDiscoverSection(),
            ),
            const SizedBox(width: 24),
            SizedBox(
              width: MediaQuery.of(context).size.width / 4.7,
              child: _buildDevelopSection(),
            ),
          ],
        ),
        // Spacer to push "More templates" to the right
        const Spacer(),
        // Right side - More templates link
        Align(
          alignment: Alignment.topRight,
          child: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                // Handle more templates tap
              },
              child: Text(
                'More templates',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textBlue,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDiscoverSection() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // Clear chat data for fresh start when navigating from Discover section
          final webHomeProviderStatic = Provider.of<WebHomeProviderStatic>(context, listen: false);
          final webHomeProvider = Provider.of<WebHomeProvider>(context, listen: false);
          
          // Reset both providers to ensure complete data clearing
          webHomeProviderStatic.resetConversation(preserveScreenIndex: true);
          webHomeProvider.resetConversation(preserveScreenIndex: true);
          
          // Clear any additional chat-related data
          webHomeProviderStatic.clearMessages();
          webHomeProvider.clearMessages();
          
          // Clear chat controllers
          webHomeProviderStatic.chatController.clear();
          webHomeProvider.chatController.clear();
          
          // Reset UI state completely
          webHomeProviderStatic.resetUIState(preserveScreenIndex: true);
          webHomeProvider.resetUIState(preserveScreenIndex: true);
          
          // Clear any NSL session data
          webHomeProvider.resetNslSession();
          webHomeProvider.resetModeSession();
          
          // Clear any solution session data
          webHomeProviderStatic.solutionSessionModel = null;
          webHomeProvider.solutionSessionModel = null;
          
          // Force clear any cached data
          webHomeProviderStatic.lastUserMessageForApi = '';
          webHomeProvider.lastUserMessageForApi = '';
          
          // Navigate to the screen
          webHomeProvider.currentScreenIndex = ScreenConstants.create;
          webHomeProviderStatic.currentCreateScreenIndex = 1;
        },
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: Colors.white,
            // border: Border.all(color: AppColors.greyBorder),
            borderRadius: BorderRadius.circular(AppSpacing.xxs),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    'assets/images/my_library/my_library_discover.svg', // your SVG asset path
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Discover',
                    style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Colors.black),
                  ),
                  const Spacer(),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: _showDiscoverModal,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                              color: AppColors.textGreyColor, width: 1),
                        ),
                        child: Center(
                          child: Text(
                            'i',
                            style: TextStyle(
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textGreyColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Share your industry and the solutions you need—our NSL AI will handle complete solution discovery and build it tailored to your needs.',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textGreyColor,
                  // height: 2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  Widget _buildDevelopSection() {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: () {
        print('Develop section tapped'); // Add this for debugging
        
        // Direct navigation without callbacks
        try {
          Provider.of<WebHomeProvider>(context, listen: false)
              .currentScreenIndex = ScreenConstants.createObjectScreenStatic;
        } catch (e) {
          print('Navigation error: $e');
        }
      },
      // hoverColor: Colors.grey.withOpacity(0.1),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SvgPicture.asset(
                  'assets/images/my_library/my_library_develop.svg',
                  width: 20,
                  height: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Develop',
                  style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Colors.black),
                ),
                const Spacer(),
                GestureDetector(
                  // onTap: (event) {
                  //   // Stop event propagation to prevent parent tap
                  //   event?.stopPropagation();
                  //   _showDevelopModal();
                  // },
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border:
                          Border.all(color: AppColors.textGreyColor, width: 1),
                    ),
                    child: Center(
                      child: Text(
                        'i',
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textGreyColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Enter or upload your requirement, and we\'ll extract, develop, and refine your solution with AI-guided suggestions throughout.',
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: 10,
                fontWeight: FontWeight.w400,
                color: AppColors.textGreyColor,
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

  // Widget _buildDevelopSection() {
  //   return GestureDetector(
  //     onTap: () {
  //     print('Develop section tapped'); // Add this for debugging
      
  //     // Use Future.microtask instead of addPostFrameCallback
  //     Future.microtask(() {
  //       if (mounted) {
  //         Provider.of<WebHomeProvider>(context, listen: false)
  //             .currentScreenIndex = ScreenConstants.createObjectScreenStatic;
  //       }
  //     });
  //   },
  //     child: MouseRegion(
  //        cursor: SystemMouseCursors.click,
  //       child: Container(
  //         padding: const EdgeInsets.all(AppSpacing.lg),
  //         decoration: BoxDecoration(
  //           color: Colors.white,
  //           // border: Border.all(color: AppColors.greyBorder),
  //           borderRadius: BorderRadius.circular(AppSpacing.xxs),
  //         ),
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Row(
  //               children: [
  //                 SvgPicture.asset(
  //                   'assets/images/my_library/my_library_develop.svg', // your SVG asset path
  //                   width: 20,
  //                   height: 20,
  //                 ),
  //                 const SizedBox(width: 8),
  //                 Text(
  //                   'Develop',
  //                   style: TextStyle(
  //                       fontFamily: FontManager.fontFamilyTiemposText,
  //                       fontSize: 16,
  //                       fontWeight: FontWeight.w400,
  //                       color: Colors.black),
  //                 ),
  //                 const Spacer(),
  //                 MouseRegion(
  //                   cursor: SystemMouseCursors.click,
  //                   child: GestureDetector(
  //                     onTap: _showDevelopModal,
  //                     child: Container(
  //                       width: 16,
  //                       height: 16,
  //                       decoration: BoxDecoration(
  //                         shape: BoxShape.circle,
  //                         border:
  //                             Border.all(color: AppColors.textGreyColor, width: 1),
  //                       ),
  //                       child: Center(
  //                         child: Text(
  //                           'i',
  //                           style: TextStyle(
  //                             fontFamily: FontManager.fontFamilyTiemposText,
  //                             fontSize: 10,
  //                             fontWeight: FontWeight.w500,
  //                             color: AppColors.textGreyColor,
  //                           ),
  //                         ),
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //             const SizedBox(height: 12),
  //             Text(
  //               'Enter or upload your requirement, and we\'ll extract, develop, and refine your solution with AI-guided suggestions throughout.',
  //               style: TextStyle(
  //                 fontFamily: FontManager.fontFamilyTiemposText,
  //                 fontSize: 10,
  //                 fontWeight: FontWeight.w400,
  //                 color: AppColors.textGreyColor,
  //                 // height: 2,
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildBottomSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTabBar(),
        const SizedBox(height: AppSpacing.xs),
        Expanded(
          child: _buildTable(),
        ),
        const SizedBox(height: 16),
        _buildPagination(),
      ],
    );
  }

  Widget _buildTabBar() {
    final tabs = ['Recent', 'Favourite'];

    return Row(
      children: [
        ...tabs.map((tab) => _buildTabItem(tab)),
        const Spacer(),
        _showSearchBar
            ? SearchBarWidget(
                controller: _searchController,
                onClose: _toggleSearchBar,
              )
            : GestureDetector(
                onTap: _toggleSearchBar,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: Container(
                    margin: const EdgeInsets.only(right: AppSpacing.sm),
                    height: 36,
                    child: HoverableSearchIcon(),
                  ),
                ),
              ),
      ],
    );
  }

  Widget _buildTabItem(String tab) {
    final isSelected = _selectedTab == tab;

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTab = tab;
            _currentPage = 0; // Reset to first page when changing tabs
          });
        },
        child: Container(
          margin: const EdgeInsets.only(right: AppSpacing.xs),
          padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md, vertical: AppSpacing.xs),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.black : Colors.transparent,
            borderRadius: BorderRadius.circular(2),
          ),
          child: Text(
            tab,
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: isSelected ? Colors.white : AppColors.textGreyColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      child: Column(
        children: [
          _buildTableHeader(),
          Expanded(
            child: ListView.builder(
              itemCount: _paginatedData.length,
              itemBuilder: (context, index) {
                return _buildTableRow(_paginatedData[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
          top: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // File Name - Left aligned
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'File Name',
                style: TextStyle(
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: Colors.black),
              ),
            ),
          ),
          // Last Opened
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Last Opened',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          // Favorites column - empty header
          Expanded(
            flex: 1,
            child: Container(),
          ),
          // Sort With label
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                'Sort With:',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textGreyColor,
                ),
              ),
            ),
          ),
          // Right group - Project, Solution, Object, Role
          Expanded(
            flex: 3,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                    child: Center(
                        child: _buildSortableHeader('Project', 'project'))),
                Expanded(
                    child: Center(
                        child: _buildSortableHeader('Solution', 'solution'))),
                Expanded(
                    child: Center(
                        child: _buildSortableHeader('Object', 'object'))),
                Expanded(
                    child: Center(child: _buildSortableHeader('Role', 'role'))),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortableHeader(String title, String column) {
    // Only allow filtering for category columns
    final isFilterableColumn =
        ['project', 'solution', 'object', 'role'].contains(column);
    final isCurrentFilter =
        _selectedCategoryFilter == _getCategoryFromColumn(column);
    final isHovered = _hoveredHeaderColumn == column;

    return MouseRegion(
      cursor: isFilterableColumn
          ? SystemMouseCursors.click
          : SystemMouseCursors.basic,
      onEnter: (_) => setState(() => _hoveredHeaderColumn = column),
      onExit: (_) => setState(() => _hoveredHeaderColumn = null),
      child: GestureDetector(
        onTap: isFilterableColumn ? () => _filterByCategory(column) : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: 10,
                fontWeight: FontWeight.w400,
                color: isCurrentFilter ? Colors.black : AppColors.textGreyColor,
              ),
            ),
            // Always reserve space for the icon to prevent layout shift
            const SizedBox(width: 4),

            SizedBox(
              width: 10,
              height: 10,
              child: isFilterableColumn && isHovered
                  ? SvgPicture.asset(
                      'assets/images/my_library/my_library_sort.svg', // replace with your actual SVG path
                      width: 10,
                      height: 10,
                    )
                  : const SizedBox
                      .shrink(), // avoids null, renders empty widget
            ),
          ],
        ),
      ),
    );
  }

  String? _getCategoryFromColumn(String column) {
    switch (column) {
      case 'project':
        return 'Project';
      case 'solution':
        return 'Solution';
      case 'object':
        return 'Object';
      case 'role':
        return 'Role';
      default:
        return null;
    }
  }

  void _filterByCategory(String column) {
    final category = _getCategoryFromColumn(column);
    setState(() {
      if (_selectedCategoryFilter == category) {
        // If already selected, clear the filter
        _selectedCategoryFilter = null;
      } else {
        // Set new filter
        _selectedCategoryFilter = category;
      }
      _currentPage = 0; // Reset to first page when filtering
    });
  }

  Widget _buildTableRow(Map<String, dynamic> item, int index) {
    final isEvenRow = index % 2 == 0;
    final isHovered = _hoveredRowIndex == index;
    final isFavorite = item['isFavorite'] ?? false;

    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredRowIndex = index),
      onExit: (_) => setState(() => _hoveredRowIndex = null),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: isEvenRow ? Colors.grey.shade50 : Colors.white,
          border: Border(
            bottom: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
          ),
        ),
        child: Row(
          children: [
            // File Name - Left aligned
            Expanded(
              flex: 3,
              child: Row(
                children: [
                  _buildTypeIcon(item['type']),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['fileName'],
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimaryLight,
                          ),
                        ),
                        if (item['type'].isNotEmpty)
                          Text(
                            item['type'],
                            style: TextStyle(
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                              color: AppColors.textGreyColor,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Last Opened
            Expanded(
              flex: 2,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  _formatDate(item['lastOpened']),
                  style: TextStyle(
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textGreyColor,
                  ),
                ),
              ),
            ),
            // Spacer to push favorite star to the right position

            // Favorites column - hover-based star visibility (positioned to align with "Sort With:")
            Expanded(
              flex: 1,
              child: Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 40.0),
                  child: Visibility(
                    visible: isHovered || isFavorite,
                    child: MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            item['isFavorite'] = !item['isFavorite'];
                          });
                        },
                        child: Icon(
                          item['isFavorite']
                              ? Icons.star
                              : Icons
                                  .star_border, // ⭐ Toggle between filled and border
                          size: 20,
                          color: item['isFavorite']
                              ? Colors.amber
                              : AppColors.textGreyColor, // 🎨 Color toggle
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            Expanded(
              flex: 1,
              child: Container(),
            ),
            Expanded(
              flex: 3,
              child: Container(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeIcon(String type) {
    String svgAsset;

    switch (type) {
      case 'Role':
        svgAsset = 'assets/images/my_library/my_library_role.svg';
        break;
      case 'Object':
        svgAsset = 'assets/images/my_library/my_library_object.svg';
        break;
      case 'Solution':
        svgAsset = 'assets/images/my_library/my_library_solution.svg';
        break;
      case 'Project':
        svgAsset = 'assets/images/my_library/my_library_project.svg';
        break;
      default:
        svgAsset = 'assets/icons/default.svg';
    }

    return Center(
      child: CustomImage.asset(
        svgAsset,
        width: 32,
        height: 32,
        fit: BoxFit.contain,
      ).toWidget(),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday, ${date.day}/${date.month}/${date.year}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildPagination() {
    if (_totalPages <= 1) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppSpacing.md),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Navigation buttons
          Row(
            children: [
              // Previous button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_left, size: 20),
                onPressed: _currentPage > 0
                    ? () {
                        setState(() {
                          _currentPage--;
                        });
                      }
                    : null,
              ),
              const SizedBox(width: 8),
              // Next button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_right, size: 20),
                onPressed: _currentPage < _totalPages - 1
                    ? () {
                        setState(() {
                          _currentPage++;
                        });
                      }
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModal({required String title, required Widget content}) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 500),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.greyBorder, width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimaryLight,
                      ),
                    ),
                  ),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: content,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscoverModalContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModalStep(
          'Step 1: Describe Your solution',
          'Specify your business industry, organization size, operational requirements, and geographic locations for the solution.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 2: AI Analysis',
          'Our AI engine automatically discovers and lists out the roles, entities, and workflows for your solution',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 3: Review & Customize',
          'Validate the document in details and customize any components you want to modify with prompt',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 4: Development',
          'Once you have confirmed the final components, proceed with the development of your solution using the discovered framework',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 5: Testing',
          'Test your completed solution to ensure it works as expected.',
        ),
      ],
    );
  }

  Widget _buildDevelopModalContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModalStep(
          'Step 1: Requirements Definition',
          'Craft your detailed solution requirements through our intuitive prompt interface, providing comprehensive information about your business needs.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 2: Intelligent Parsing',
          'Our AI engine intelligently extracts and identifies the essential components from your comprehensive requirements.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 3: Solution Architecture',
          'Seamlessly add new components and fine-tune every aspect of your solution framework to perfectly match your vision.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 4: AI-Powered Optimization',
          'Leverage smart AI recommendations to enhance and optimize your solution design for maximum effectiveness.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 5: Industry Alignment',
          'Effortlessly import industry-standard components and best practices tailored to your business sector.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 6: Solution Validation',
          'Thoroughly review your complete solution framework, including all components and workflow structures.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 7: Implementation',
          '',
        ),
      ],
    );
  }

  Widget _buildModalStep(String title, String description) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimaryLight,
          ),
        ),
        if (description.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: AppColors.textGreyColor,
              height: 1.4,
            ),
          ),
        ],
      ],
    );
  }
}

class CustomTabHeader extends StatefulWidget {
  @override
  _CustomTabHeaderState createState() => _CustomTabHeaderState();
}

class _CustomTabHeaderState extends State<CustomTabHeader> {
  String _selectedTab = 'My Library';
  String? _hoveredTab;

  final List<_TabItem> _tabs = [
    _TabItem('My Library', 'assets/images/my_library/my_library_icon.svg'),
    _TabItem('Organizer', 'assets/images/my_library/my_library_organizer.svg'),
    _TabItem('Testing', 'assets/images/my_library/my_library_testing.svg'),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left Placeholder to balance layout
          const SizedBox(width: 100),

          // Center Tab Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: _tabs.map((tab) {
              final bool isSelected = tab.label == _selectedTab;
              final bool isHovered = tab.label == _hoveredTab;

              final Color bgColor = isSelected
                  ? Colors.black
                  : isHovered
                      ? Colors.white
                      : Colors.transparent;

              final Color textColor = isSelected
                  ? Colors.white
                  : isHovered
                      ? Colors.black
                      : Colors.black;

              final Color iconColor = textColor;

              final BorderSide borderSide = isHovered && !isSelected
                  ? BorderSide(color: Colors.black)
                  : BorderSide(color: Colors.transparent);

              return MouseRegion(
                onEnter: (_) => setState(() => _hoveredTab = tab.label),
                onExit: (_) => setState(() => _hoveredTab = null),
                child: GestureDetector(
                  onTap: () => setState(() => _selectedTab = tab.label),
                  child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 14, vertical: 8),
                      decoration: BoxDecoration(
                        color: bgColor,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.fromBorderSide(borderSide),
                      ),
                      child: Row(
                        children: [
                          CustomImage.asset(
                            tab.svgAssetPath,
                            width: 12,
                            height: 12,
                            fit: BoxFit.contain,
                            color: iconColor,
                          ).toWidget(),
                          const SizedBox(width: 6),
                          Text(
                            tab.label,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'TiemposText',
                              color: textColor,
                            ),
                          ),
                        ],
                      )),
                ),
              );
            }).toList(),
          ),

          // Right placeholder to balance layout
          const SizedBox(width: 100),
        ],
      ),
    );
  }
}

class _TabItem {
  final String label;
  final String svgAssetPath; // path to SVG image

  _TabItem(this.label, this.svgAssetPath);
}

/// Search bar widget that appears when search icon is clicked
class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
                isDense: true,
                hoverColor: Colors.transparent,
              ),
              style: TextStyle(
                fontSize: 14,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              // Auto-focus when search bar appears
              autofocus: true,
            ),
          ),

          // Close button
          GestureDetector(
            onTap: onClose,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Icon(Icons.close, size: 18, color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HoverableSearchIcon extends StatefulWidget {
  const HoverableSearchIcon({super.key});

  @override
  _HoverableSearchIconState createState() => _HoverableSearchIconState();
}

class _HoverableSearchIconState extends State<HoverableSearchIcon> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: CustomImage.asset(
        'assets/images/my_library/my_library_search.svg',
        width: 18,
        height: 18,
        fit: BoxFit.contain,
        color: _isHovered ? Colors.blue : null, // <-- Change color on hover
      ).toWidget(),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.onPressed == null
                ? Colors.grey.shade200
                : (isHovered ? Color(0xff0058FF) : Colors.grey.shade300),
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: widget.onPressed == null
              ? Colors.grey.shade400
              : (isHovered ? Color(0xff0058FF) : Colors.black),
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
